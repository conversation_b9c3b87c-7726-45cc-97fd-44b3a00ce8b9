import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

public class Javadownloader {
    private final String fileUrl;
    private final String savePath;
    private final int threadCount;
    private long fileSize;
    private final List<DownloadTask> tasks;
    private final ExecutorService executor;

    public Javadownloader(String fileUrl, String savePath, int threadCount) {
        this.fileUrl = fileUrl;
        this.savePath = savePath;
        this.threadCount = threadCount;
        this.tasks = new ArrayList<>();
        this.executor = Executors.newFixedThreadPool(threadCount);
    }

    public void download() throws IOException, InterruptedException, ExecutionException {
        // 获取文件总大小
        fileSize = getFileSize();
        System.out.println("文件总大小: " + fileSize + " bytes");

        if (fileSize == -1 || !supportsRange(fileUrl)) {
            // Fallback to single-threaded download
            singleThreadDownload(fileUrl, savePath);
        } else {
            // Multi-threaded download as before
            // 创建临时目录
            Path tempDir = Paths.get(savePath + "_temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectory(tempDir);
            }

            // 计算每个线程下载的字节范围
            long chunkSize = fileSize / threadCount;
            List<Future<Boolean>> futures = new ArrayList<>();

            // 创建并提交下载任务
            for (int i = 0; i < threadCount; i++) {
                long start = i * chunkSize;
                long end = (i == threadCount - 1) ? fileSize - 1 : start + chunkSize - 1;
                String partFileName = tempDir + File.separator + "part_" + i;

                DownloadTask task = new DownloadTask(fileUrl, partFileName, start, end);
                tasks.add(task);
                futures.add(executor.submit(task));
            }

            // 等待所有任务完成
            for (Future<Boolean> future : futures) {
                if (!future.get()) {
                    throw new RuntimeException("下载失败");
                }
            }

            // 合并文件
            mergeFiles(tempDir);

            // 清理临时文件
            cleanTempFiles(tempDir);

            executor.shutdown();
            System.out.println("下载完成: " + savePath);
        }
    }

    private long getFileSize() throws IOException {
        HttpURLConnection conn = null;
        try {
            conn = openConnectionFollowRedirects(fileUrl);
            conn.setRequestMethod("HEAD");
            return conn.getContentLengthLong();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    private void mergeFiles(Path tempDir) throws IOException {
        try (OutputStream output = new BufferedOutputStream(new FileOutputStream(savePath))) {
            for (int i = 0; i < threadCount; i++) {
                Path partFile = tempDir.resolve("part_" + i);
                Files.copy(partFile, output);
            }
        }
    }

    private void cleanTempFiles(Path tempDir) throws IOException {
        for (int i = 0; i < threadCount; i++) {
            Path partFile = tempDir.resolve("part_" + i);
            Files.deleteIfExists(partFile);
        }
        Files.deleteIfExists(tempDir);
    }

    private boolean supportsRange(String fileUrl) throws IOException {
        HttpURLConnection conn = null;
        try {
            conn = openConnectionFollowRedirects(fileUrl);
            conn.setRequestProperty("Range", "bytes=0-0");
            int responseCode = conn.getResponseCode();
            return responseCode == 206;
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    private void singleThreadDownload(String fileUrl, String savePath) throws IOException {
        HttpURLConnection conn = null;
        InputStream input = null;
        OutputStream output = null;
        try {
            System.out.println("Starting single-threaded download from: " + fileUrl);
            conn = openConnectionFollowRedirects(fileUrl);
            
            // Print response headers for debugging
            System.out.println("Response Headers:");
            conn.getHeaderFields().forEach((key, values) -> {
                if (key != null) {
                    System.out.println(key + ": " + String.join(", ", values));
                }
            });
            
            int responseCode = conn.getResponseCode();
            System.out.println("Response Code: " + responseCode);
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                // Try to read error stream if available
                try (InputStream errorStream = conn.getErrorStream()) {
                    if (errorStream != null) {
                        String errorResponse = new String(errorStream.readAllBytes());
                        System.out.println("Error Response: " + errorResponse);
                    }
                } catch (Exception e) {
                    System.out.println("Could not read error stream: " + e.getMessage());
                }
                throw new IOException("Server returned HTTP response code: " + responseCode);
            }

            input = conn.getInputStream();
            output = new FileOutputStream(savePath);

            byte[] buffer = new byte[8192]; // Increased buffer size
            int bytesRead;
            long totalBytesRead = 0;
            
            System.out.println("Starting download...");
            while ((bytesRead = input.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
                if (totalBytesRead % (1024 * 1024) == 0) { // Log every 1MB
                    System.out.println("Downloaded: " + totalBytesRead + " bytes");
                }
            }
            System.out.println("Download completed successfully. Total bytes downloaded: " + totalBytesRead);
        } catch (IOException e) {
            System.err.println("Error during download: " + e.getMessage());
            throw e;
        } finally {
            try {
                if (input != null) input.close();
                if (output != null) output.close();
                if (conn != null) conn.disconnect();
            } catch (IOException e) {
                System.err.println("Error closing resources: " + e.getMessage());
            }
        }
    }

    private static HttpURLConnection openConnectionFollowRedirects(String fileUrl) throws IOException {
        try {
            URL url = URI.create(fileUrl).toURL();
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            // Let HttpURLConnection handle redirects automatically
            conn.setInstanceFollowRedirects(true);

            // Set some common request headers
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Accept-Language", "en-US,en;q=0.9");
            conn.setRequestProperty("Connection", "keep-alive");

            // Set a reasonable timeout
            conn.setConnectTimeout(30000);
            conn.setReadTimeout(30000);

            // Disable caching
            conn.setUseCaches(false);

            // Make the request
            conn.connect();

            // Print the final URL after all redirects
            System.out.println("Final URL after redirects: " + conn.getURL());

            // Print response headers for debugging
            System.out.println("Response Headers:");
            conn.getHeaderFields().forEach((key, values) -> {
                if (key != null) {
                    System.out.println(key + ": " + String.join(", ", values));
                }
            });

            int responseCode = conn.getResponseCode();
            System.out.println("Final response code: " + responseCode);

            if (responseCode != HttpURLConnection.HTTP_OK && responseCode != HttpURLConnection.HTTP_PARTIAL) {
                // Try to read error stream if available
                try (InputStream errorStream = conn.getErrorStream()) {
                    if (errorStream != null) {
                        String errorResponse = new String(errorStream.readAllBytes());
                        System.out.println("Error Response: " + errorResponse);
                    }
                } catch (Exception e) {
                    System.out.println("Could not read error stream: " + e.getMessage());
                }
                throw new IOException("Server returned HTTP response code: " + responseCode + " for URL: " + fileUrl);
            }

            return conn;
        } catch (Exception e) {
            throw new IOException("Failed to create connection to URL: " + fileUrl, e);
        }
    }

    private static class DownloadTask implements Callable<Boolean> {
        private final String fileUrl;
        private final String partFileName;
        private final long start;
        private final long end;

        public DownloadTask(String fileUrl, String partFileName, long start, long end) {
            this.fileUrl = fileUrl;
            this.partFileName = partFileName;
            this.start = start;
            this.end = end;
        }

        @Override
        public Boolean call() throws Exception {
            HttpURLConnection conn = null;
            InputStream input = null;
            RandomAccessFile output = null;

            try {
                conn = openConnectionFollowRedirects(fileUrl);
                conn.setRequestProperty("Range", "bytes=" + start + "-" + end);
                int responseCode = conn.getResponseCode();

                if (responseCode != HttpURLConnection.HTTP_PARTIAL) {
                    throw new IOException("服务器不支持断点续传");
                }

                input = conn.getInputStream();
                output = new RandomAccessFile(partFileName, "rw");
                output.seek(0);

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }

                return true;
            } finally {
                if (input != null) input.close();
                if (output != null) output.close();
                if (conn != null) conn.disconnect();
            }
        }
    }

    public static void main(String[] args) {
        String fileUrl = "http://cosmoon.ddns.net/btos.7z"; // 替换为实际下载链接
        String savePath = "btos.7z"; // 本地保存路径
        int threadCount = 4; // 线程数

        Javadownloader downloader = new Javadownloader(fileUrl, savePath, threadCount);
        try {
            downloader.download();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}